# 泰勒公式推导方式二

在级数这一节，使用了先设定然后推导的方式<br>
也就是要将函数表达到多项式幂函数相加的形式     <br>
即 $f(x)=a_0+a_1(x-x_0)+a_2(x-x_0)^2+...+a_n(x-x_0)^n+O(x-x_0)^n$<br>

$f(x)=\sum_{i=0}^{\infty}{a_i(x-x_0)^i}$幂函数是存在n阶导数的，所以可以依次求n阶导函数，得到：<br>

$f^{(0)}(x)=a_0, f^{(1)}(x)=a_1+2a_2(x-x_0)+3a_3(x-x_0)^2+...$$f^{(n)}(x)=n!a_n+(n+1)!a_{n+1}(x-x_0)+\frac{(n+2)!}{2}a_{n+2}(x-x_0)^2+...$然后可以带入 $x_0$ ，得到 $f^{(0)}(x_0)=a_0, f^{(1)}(x_0)=a_1,f^{(2)}(x_0)=2!a_2,...,f^{(n)}(x_0)=n!a_n$<br>
所以就可以带入得到泰勒公式<br>

$f(x)=f(x_0)+f'(x_0)(x-x_0)+\frac{f^{(2)}(x_0)}{2!}(x-x_0)^2+...+\frac{f^{(n)}(x_0)}{n!}(x-x_0)^n+O(x-x_0)^n$<br>

[https://zhuanlan.zhihu.com/p/622433720](https://zhuanlan.zhihu.com/p/622433720)<br>



